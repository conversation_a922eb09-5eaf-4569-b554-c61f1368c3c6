---
- name: Add group git
  ansible.builtin.group:
    name: "{{ gitlab_group }}"
    state: present

- name: Add user git
  ansible.builtin.user:
    name: "{{ gitlab_user }}"
    state: present
    group: "{{ gitlab_group }}"

- name: Create required directories
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    owner: "{{ gitlab_user }}"
    group: "{{ gitlab_group }}"
    mode: 0755
  loop:
    - "{{ gitlab_root_dir }}"
    - "{{ gitlab_data_dir }}"
    - "{{ gitlab_log_dir }}"
    - "{{ gitlab_config_dir }}"
    - "{{ gitlab_ssl_dir }}"

- name: Copy gitlab.rb
  ansible.builtin.template:
    src: gitlab.rb.j2
    dest: "{{ gitlab_config_dir }}/gitlab.rb"
    owner: "{{ gitlab_user }}"
    group: "{{ gitlab_group }}"
    mode: 0644

- name: Create certificate
  ansible.builtin.import_role:
    name: freeipa-certgen

- name: <PERSON><PERSON> docker-compose.yml
  ansible.builtin.template:
    src: docker-compose.yml.j2
    dest: "{{ gitlab_root_dir }}/docker-compose.yml"
    owner: "{{ gitlab_user }}"
    group: "{{ gitlab_group }}"
    mode: 0644

- name: Check if gitlab docker container is running
  ansible.builtin.command: docker ps -f name=gitlab -q
  register: gitlab_container
  changed_when: false

- name: Start gitlab docker container
  ansible.builtin.command: docker-compose -f {{ gitlab_root_dir }}/docker-compose.yml up -d
  when: gitlab_container.stdout == ""
  changed_when: true

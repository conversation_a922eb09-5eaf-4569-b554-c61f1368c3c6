---
# defaults file for roles/gitlab
gitlab_user: git
gitlab_group: git

gitlab_root_dir: /data/gitlab
gitlab_data_dir: "{{ gitlab_root_dir }}/data"
gitlab_log_dir: "{{ gitlab_root_dir }}/logs"
gitlab_config_dir: "{{ gitlab_root_dir }}/config"
gitlab_ssl_dir: "{{ gitlab_root_dir }}/ssl"
gitlab_backup_dir: "/backup"

gitlab_version: "17.9.2-ce.0"

**********************: "1024m"

# Docker Logging
gitlab_docker_log_driver: "json-file"
gitlab_docker_log_max_size: "300m"
gitlab_docker_log_max_file: "3"
